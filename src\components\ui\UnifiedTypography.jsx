'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedTypography - Ujednolicony system typografii BAKASANA
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

// Heading Components - Enhanced with Playfair Display and Breathing
export function HeroTitle({ children, className = '', breathing = true, ...props }) {
  return (
    <h1
      className={cn(
        "font-playfair font-normal text-charcoal leading-tight",
        "tracking-wide mb-8 text-center",
        "text-hero heading-playfair hero-title-shadow",
        breathing && "breathe-text",
        className
      )}
      {...props}
    >
      {children}
    </h1>
  );
}

export function SectionTitle({ children, level = 2, className = '', breathing = true, warmAccent, ...props }) {
  const Tag = `h${level}`;

  return (
    <Tag
      className={cn(
        "font-playfair font-normal text-charcoal leading-breathing",
        "text-display-xl tracking-normal",
        "mb-12 text-center heading-playfair",
        breathing && "breathe-gentle",
        className
      )}
      {...props}
    >
      {warmAccent ? (
        <>
          {children.split(warmAccent)[0]}
          <span className="warm-underline active">{warmAccent}</span>
          {children.split(warmAccent)[1]}
        </>
      ) : (
        children
      )}
    </Tag>
  );
}

export function CardTitle({ children, level = 3, className = '', breathing = false, ...props }) {
  const Tag = `h${level}`;

  return (
    <Tag
      className={cn(
        "font-playfair font-normal text-charcoal leading-breathing",
        "text-heading-lg tracking-normal",
        "mb-6", // Increased for more breathing
        breathing && "breathe-gentle",
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function SubTitle({ children, className = '', ...props }) {
  return (
    <h4
      className={cn(
        "font-lato font-light text-charcoal",
        "tracking-[0.15em] leading-relaxed",
        "mb-6 text-center opacity-70 hero-subtitle-spacing",
        className
      )}
      {...props}
    >
      {children}
    </h4>
  );
}

// Body Text Components - Enhanced with breathing
export function BodyText({ children, size = 'md', className = '', breathing = false, ...props }) {
  const sizeClasses = {
    sm: "text-sm leading-breathing",
    md: "text-base leading-breathing", 
    lg: "text-lg leading-breathing"
  };

  return (
    <p
      className={cn(
        "font-lato font-light text-charcoal-light body-breathing",
        sizeClasses[size],
        "mb-8", // Increased for more breathing
        breathing && "breathe-gentle",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function LeadText({ children, className = '', ...props }) {
  return (
    <p
      className={cn(
        "font-lato font-light text-charcoal",
        "text-xl leading-relaxed",
        "mb-8 max-w-3xl mx-auto text-center",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function SmallText({ children, className = '', ...props }) {
  return (
    <p
      className={cn(
        "font-lato font-light text-sage",
        "text-sm leading-relaxed",
        "mb-4",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

// Special Text Components - Enhanced with breathing and warm accents
export function HandwritingText({ children, className = '', breathing = true, ...props }) {
  return (
    <span
      className={cn(
        "text-handwriting",
        breathing && "breathe-gentle",
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

export function Quote({ children, author, className = '', breathing = true, ...props }) {
  return (
    <blockquote
      className={cn(
        "font-caveat text-rose-gold-warm",
        "text-2xl leading-breathing text-center",
        "mb-12 max-w-2xl mx-auto", // Increased margin for breathing
        "relative",
        breathing && "breathe-gentle",
        className
      )}
      {...props}
    >
      <span className="text-4xl opacity-30 absolute -top-4 -left-4 text-rose-gold">"</span>
      {children}
      <span className="text-4xl opacity-30 absolute -bottom-8 -right-4 text-rose-gold">"</span>
      {author && (
        <cite className="block font-lato font-light text-sage text-sm mt-6 not-italic">
          — {author}
        </cite>
      )}
    </blockquote>
  );
}

export function Badge({ children, variant = 'default', className = '', ...props }) {
  const variants = {
    default: "bg-enterprise-brown text-sanctuary",
    outline: "border border-enterprise-brown text-enterprise-brown bg-transparent",
    warm: "bg-terra text-sanctuary",
    minimal: "bg-whisper text-charcoal"
  };

  return (
    <span
      className={cn(
        "inline-block px-3 py-1 text-xs font-lato font-medium",
        "uppercase tracking-[2px] transition-all duration-300",
        variants[variant],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

// Navigation Text - Enhanced with elegant hover effects
export function NavLink({ children, active = false, className = '', ...props }) {
  return (
    <span
      className={cn(
        "relative font-lato font-light uppercase tracking-[2px] group cursor-pointer",
        "transition-all duration-300 ease-out",
        active
          ? "text-enterprise-brown opacity-100 text-sm"
          : "text-charcoal-light opacity-60 hover:opacity-100 hover:text-enterprise-brown text-sm",
        className
      )}
      {...props}
    >
      {children}
      {/* Elegant underline that flows from center */}
      <span
        className={cn(
          "absolute -bottom-1 left-1/2 h-[1px] bg-gradient-to-r from-transparent via-enterprise-brown to-transparent",
          "transition-all duration-300 ease-out transform -translate-x-1/2",
          active
            ? "w-full opacity-100"
            : "w-0 opacity-0 group-hover:w-full group-hover:opacity-100"
        )}
      />
    </span>
  );
}

// Form Labels
export function FormLabel({ children, required = false, className = '', ...props }) {
  return (
    <label
      className={cn(
        "block text-sm font-lato font-light text-charcoal",
        "mb-2 tracking-wide",
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span className="text-terra ml-1" aria-label="wymagane">*</span>
      )}
    </label>
  );
}

// Stats/Numbers
export function StatNumber({ children, className = '', ...props }) {
  return (
    <div
      className={cn(
        "font-montserrat font-extralight text-enterprise-brown",
        "text-5xl leading-none mb-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function StatLabel({ children, className = '', ...props }) {
  return (
    <div
      className={cn(
        "font-lato font-medium text-sage",
        "text-xs uppercase tracking-[2px]",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Utility Components - Enhanced with breathing
export function Divider({ className = '', breathing = true, ...props }) {
  return (
    <div 
      className={cn(
        "section-divider",
        !breathing && "my-12", // Fallback if breathing is disabled
        className
      )}
      {...props}
    />
  );
}

export function WarmDivider({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center justify-center my-16", // More breathing room
        className
      )}
      {...props}
    >
      <div className="flex-1 h-px bg-gradient-to-r from-transparent via-rose-gold-light to-transparent max-w-32"></div>
      <div className="w-2 h-2 bg-rose-gold transform rotate-45 mx-8 breathe-glow"></div>
      <div className="flex-1 h-px bg-gradient-to-r from-rose-gold-light via-transparent to-transparent max-w-32"></div>
    </div>
  );
}

export function OrganicAccent({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "w-12 h-0.5 bg-gradient-to-r from-transparent via-enterprise-brown to-transparent",
        "mx-auto mb-8 opacity-60",
        className
      )}
      {...props}
    />
  );
}