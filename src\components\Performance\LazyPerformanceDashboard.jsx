/**
 * LAZY PERFORMANCE DASHBOARD
 * Dynamic import wrapper for EnterprisePerformanceDashboard
 * Reduces initial bundle size by loading component on demand
 */

import { lazy, Suspense } from 'react';
import { LoadingStates } from './LoadingStates';

// Dynamic import with proper error boundary
const EnterprisePerformanceDashboard = lazy(() => 
  import('./EnterprisePerformanceDashboard').catch(error => {
    console.error('Failed to load Performance Dashboard:', error);
    // Return a fallback component
    return {
      default: () => (
        <div className="min-h-screen flex items-center justify-center bg-sanctuary">
          <div className="text-center p-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-temple-gold/20 rectangular flex items-center justify-center">
              <span className="text-temple-gold text-2xl">⚠️</span>
            </div>
            <h2 className="text-xl font-playfair text-charcoal mb-2">
              Dashboard Unavailable
            </h2>
            <p className="text-ash text-sm">
              Performance dashboard could not be loaded. Please refresh the page.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-4 px-6 py-2 bg-temple-gold text-sanctuary rectangular hover:bg-enterprise-brown transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      )
    };
  })
);

// Loading component with proper styling
const DashboardLoader = () => (
  <div className="min-h-screen bg-sanctuary">
    {/* Header skeleton */}
    <div className="bg-white border-b border-stone-light/30">
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-stone-light/50 rectangular animate-pulse"></div>
            <div className="h-4 w-96 bg-stone-light/30 rectangular animate-pulse"></div>
          </div>
          <div className="h-10 w-32 bg-temple-gold/20 rectangular animate-pulse"></div>
        </div>
      </div>
    </div>

    {/* Main content skeleton */}
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Stats grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white p-6 rectangular border border-stone-light/30">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 w-20 bg-stone-light/50 rectangular animate-pulse"></div>
              <div className="h-8 w-8 bg-temple-gold/20 rectangular animate-pulse"></div>
            </div>
            <div className="h-8 w-16 bg-stone-light/50 rectangular animate-pulse mb-2"></div>
            <div className="h-3 w-24 bg-stone-light/30 rectangular animate-pulse"></div>
          </div>
        ))}
      </div>

      {/* Charts skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white p-6 rectangular border border-stone-light/30">
            <div className="h-6 w-48 bg-stone-light/50 rectangular animate-pulse mb-6"></div>
            <div className="h-64 w-full bg-stone-light/20 rectangular animate-pulse"></div>
          </div>
        ))}
      </div>

      {/* Table skeleton */}
      <div className="bg-white rectangular border border-stone-light/30">
        <div className="p-6 border-b border-stone-light/30">
          <div className="h-6 w-40 bg-stone-light/50 rectangular animate-pulse"></div>
        </div>
        <div className="p-6">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center justify-between py-4 border-b border-stone-light/20 last:border-b-0">
              <div className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-stone-light/30 rectangular animate-pulse"></div>
                <div className="space-y-2">
                  <div className="h-4 w-32 bg-stone-light/50 rectangular animate-pulse"></div>
                  <div className="h-3 w-24 bg-stone-light/30 rectangular animate-pulse"></div>
                </div>
              </div>
              <div className="h-4 w-16 bg-stone-light/50 rectangular animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Loading indicator */}
    <div className="fixed bottom-8 right-8">
      <div className="bg-white p-4 rectangular border border-stone-light/30 shadow-elegant">
        <div className="flex items-center space-x-3">
          <LoadingStates.Lotus />
          <span className="text-sm text-ash">Loading dashboard...</span>
        </div>
      </div>
    </div>
  </div>
);

// Main component with error boundary
export default function LazyPerformanceDashboard(props) {
  return (
    <Suspense fallback={<DashboardLoader />}>
      <EnterprisePerformanceDashboard {...props} />
    </Suspense>
  );
}

// Export loading component for reuse
export { DashboardLoader };
