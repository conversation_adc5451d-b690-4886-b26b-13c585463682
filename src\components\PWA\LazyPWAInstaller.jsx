/**
 * LAZY PWA INSTALLER
 * Dynamic import wrapper for EnterprisePWAInstaller
 * Loads PWA installer only when needed to reduce initial bundle
 */

import { lazy, Suspense } from 'react';
import { LoadingStates } from '../Performance/LoadingStates';

// Dynamic import with error handling
const EnterprisePWAInstaller = lazy(() => 
  import('./EnterprisePWAInstaller').catch(error => {
    console.error('Failed to load PWA Installer:', error);
    // Return a minimal fallback
    return {
      default: () => (
        <div className="fixed bottom-4 right-4 bg-white p-4 rectangular border border-stone-light/30 shadow-elegant max-w-sm">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-temple-gold/20 rectangular flex items-center justify-center">
              <span className="text-temple-gold text-lg">📱</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-charcoal">
                Install App
              </p>
              <p className="text-xs text-ash">
                PWA installer unavailable
              </p>
            </div>
            <button 
              onClick={() => {
                // Try to trigger browser's native install prompt
                if ('serviceWorker' in navigator) {
                  window.location.reload();
                }
              }}
              className="text-xs px-3 py-1 bg-temple-gold text-sanctuary rectangular hover:bg-enterprise-brown transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )
    };
  })
);

// Minimal loading state for PWA installer
const PWAInstallerLoader = () => (
  <div className="fixed bottom-4 right-4 bg-white p-4 rectangular border border-stone-light/30 shadow-elegant max-w-sm">
    <div className="flex items-center space-x-3">
      <div className="w-10 h-10 bg-temple-gold/20 rectangular animate-pulse"></div>
      <div className="flex-1 space-y-2">
        <div className="h-4 w-24 bg-stone-light/50 rectangular animate-pulse"></div>
        <div className="h-3 w-32 bg-stone-light/30 rectangular animate-pulse"></div>
      </div>
      <div className="w-12 h-6 bg-temple-gold/30 rectangular animate-pulse"></div>
    </div>
  </div>
);

// Main component - only renders if PWA is installable
export default function LazyPWAInstaller(props) {
  // Check if PWA installation is supported and relevant
  const isPWASupported = typeof window !== 'undefined' && 
    ('serviceWorker' in navigator || 'standalone' in window.navigator);

  // Don't render if PWA is not supported
  if (!isPWASupported) {
    return null;
  }

  return (
    <Suspense fallback={<PWAInstallerLoader />}>
      <EnterprisePWAInstaller {...props} />
    </Suspense>
  );
}

// Export loading component for reuse
export { PWAInstallerLoader };
