/**
 * INLINE STYLES MIGRATION - CSS CLASSES
 * Replacement classes for all inline styles found in components
 * Using design tokens for consistency and maintainability
 */

/* =============================================
   HERO SECTION STYLES
   ============================================= */

/* Hardware acceleration for smooth transforms */
.hero-hardware-acceleration {
  transform: translateZ(0);
  will-change: transform;
}

/* Hero title text shadow */
.hero-title-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive hero title sizing */
.hero-title-responsive {
  font-size: clamp(50px, 8vw, 90px);
}

/* Hero subtitle spacing */
.hero-subtitle-spacing {
  font-size: 16px;
  margin-top: var(--space-2xl); /* 40px equivalent */
}

/* =============================================
   FLOATING ANIMATIONS
   ============================================= */

/* Base floating animation */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
    opacity: 0.8;
  }
  50% { 
    transform: translateY(-10px); 
    opacity: 1;
  }
}

/* Floating animation variants with different durations */
.float-6s {
  animation: float 6s ease-in-out infinite;
}

.float-7s {
  animation: float 7s ease-in-out infinite;
}

.float-8s {
  animation: float 8s ease-in-out infinite;
}

.float-9s {
  animation: float 9s ease-in-out infinite;
}

.float-10s {
  animation: float 10s ease-in-out infinite;
}

.float-11s {
  animation: float 11s ease-in-out infinite;
}

.float-12s {
  animation: float 12s ease-in-out infinite;
}

/* Animation delays */
.delay-1s {
  animation-delay: 1s;
}

.delay-2s {
  animation-delay: 2s;
}

.delay-3s {
  animation-delay: 3s;
}

.delay-4s {
  animation-delay: 4s;
}

.delay-5s {
  animation-delay: 5s;
}

/* =============================================
   PARALLAX EFFECTS
   ============================================= */

/* Parallax transform for background elements */
.parallax-slow {
  transform: translateY(var(--parallax-offset, 0));
  will-change: transform;
}

/* =============================================
   GLASSMORPHISM EFFECTS
   ============================================= */

/* Glass card backgrounds using design tokens */
.glass-subtle {
  background: rgba(var(--sanctuary-rgb, 253, 252, 248), 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--temple-gold-rgb, 184, 147, 92), 0.1);
  box-shadow: var(--shadow-subtle);
}

.glass-medium {
  background: rgba(var(--sanctuary-rgb, 253, 252, 248), 0.9);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(var(--temple-gold-rgb, 184, 147, 92), 0.15);
  box-shadow: var(--shadow-elegant);
}

.glass-strong {
  background: rgba(var(--sanctuary-rgb, 253, 252, 248), 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--temple-gold-rgb, 184, 147, 92), 0.2);
  box-shadow: var(--shadow-premium);
}

/* =============================================
   PHOTO FILTER EFFECTS
   ============================================= */

/* Photo filters using CSS filters */
.photo-golden-hour {
  filter: brightness(1.1) contrast(0.95) saturate(1.2) sepia(0.1) hue-rotate(10deg);
  transition: filter var(--transition-organic);
}

.photo-authentic {
  filter: brightness(1.05) contrast(0.9) saturate(1.1) blur(0.3px);
  transition: filter var(--transition-organic);
}

.photo-memory {
  filter: brightness(1.08) contrast(0.92) saturate(1.15) blur(0.5px) sepia(0.05);
  transition: filter var(--transition-organic);
}

.photo-warm-detail {
  filter: brightness(1.12) contrast(0.88) saturate(1.25) sepia(0.08);
  transition: filter var(--transition-organic);
}

/* Photo overlay effects */
.photo-overlay-golden {
  background: linear-gradient(45deg, 
    rgba(255, 223, 186, 0.1) 0%, 
    rgba(255, 239, 213, 0.05) 100%
  );
  mix-blend-mode: soft-light;
}

.photo-overlay-warm {
  background: radial-gradient(circle at center, 
    rgba(255, 245, 235, 0.1) 0%, 
    transparent 70%
  );
  mix-blend-mode: soft-light;
}

/* =============================================
   GRADIENT OVERLAYS
   ============================================= */

/* Glass card gradient overlay */
.glass-gradient-overlay {
  background: linear-gradient(
    135deg,
    rgba(var(--temple-gold-rgb, 184, 147, 92), 0.1) 0%,
    rgba(var(--sand-rgb, 212, 175, 122), 0.05) 50%,
    rgba(var(--temple-gold-rgb, 184, 147, 92), 0.1) 100%
  );
  opacity: 0.3;
  pointer-events: none;
}

/* =============================================
   LOADING ANIMATIONS
   ============================================= */

/* Loading spinner reverse animation */
.loading-reverse {
  animation-direction: reverse;
  animation-duration: 1.5s;
}

/* Line loader animation */
@keyframes line-load {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(300%); }
}

.line-loader {
  animation: line-load 2s ease-in-out infinite;
  width: 30%;
}

/* Breathing loader animation */
@keyframes breathing {
  0%, 100% { 
    transform: scale(1); 
    opacity: 0.5; 
  }
  50% { 
    transform: scale(1.1); 
    opacity: 1; 
  }
}

.breathing-loader {
  animation: breathing 3s ease-in-out infinite;
}

/* =============================================
   LOTUS PETAL ANIMATION
   ============================================= */

@keyframes lotus-petal {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.2); 
  }
}

.lotus-petal {
  animation: lotus-petal 2s ease-in-out infinite;
}

/* =============================================
   ACCESSIBILITY CONSIDERATIONS
   ============================================= */

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .float-6s,
  .float-7s,
  .float-8s,
  .float-9s,
  .float-10s,
  .float-11s,
  .float-12s,
  .parallax-slow,
  .loading-reverse,
  .line-loader,
  .breathing-loader,
  .lotus-petal {
    animation: none !important;
    transform: none !important;
  }
  
  /* Keep static transforms for hardware acceleration */
  .hero-hardware-acceleration {
    transform: translateZ(0);
  }
}

/* =============================================
   RESPONSIVE ADJUSTMENTS
   ============================================= */

/* Mobile optimizations */
@media (max-width: 768px) {
  .hero-title-responsive {
    font-size: clamp(40px, 10vw, 60px);
  }
  
  .hero-subtitle-spacing {
    font-size: 14px;
    margin-top: var(--space-lg); /* 24px on mobile */
  }
  
  /* Reduce animation intensity on mobile */
  .float-6s,
  .float-7s,
  .float-8s,
  .float-9s,
  .float-10s,
  .float-11s,
  .float-12s {
    animation-duration: calc(var(--animation-duration, 6s) * 0.7);
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .hero-title-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .glass-subtle,
  .glass-medium,
  .glass-strong {
    border-width: 2px;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}
