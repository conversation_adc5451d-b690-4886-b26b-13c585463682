/**
 * LAZY COMPONENTS REGISTRY
 * Central registry for all dynamically imported components
 * Reduces initial bundle size by loading components on demand
 */

import { lazy } from 'react';

// =============================================
// HEAVY COMPONENTS - Load on demand
// =============================================

// Performance Dashboard (46.4 KB)
export const LazyPerformanceDashboard = lazy(() => 
  import('./Performance/LazyPerformanceDashboard')
);

// Booking Flow (40.1 KB)
export const LazyBookingFlow = lazy(() => 
  import('./Booking/LazyBookingFlow')
);

// PWA Installer (33.32 KB)
export const LazyPWAInstaller = lazy(() => 
  import('./PWA/LazyPWAInstaller')
);

// Analytics (27.5 KB)
export const LazyAnalytics = lazy(() => 
  import('./Analytics/LazyAnalytics')
);

// =============================================
// MAP COMPONENTS - Load when needed
// =============================================

// Map components are heavy and not always needed
export const LazyMapComponent = lazy(() => 
  import('./ui/MapComponent').catch(() => ({
    default: () => (
      <div className="w-full h-64 bg-stone-light/20 rectangular flex items-center justify-center">
        <span className="text-ash">Map unavailable</span>
      </div>
    )
  }))
);

// =============================================
// FORM COMPONENTS - Load when user interacts
// =============================================

// Contact forms - load when user shows intent
export const LazyContactForm = lazy(() => 
  import('./forms/ContactForm')
);

export const LazyNewsletterForm = lazy(() => 
  import('./forms/NewsletterForm')
);

// =============================================
// MEDIA COMPONENTS - Load when in viewport
// =============================================

// Image galleries - load when scrolled into view
export const LazyImageGallery = lazy(() => 
  import('./ui/ImageGallery')
);

// Video players - load when user clicks play
export const LazyVideoPlayer = lazy(() => 
  import('./ui/VideoPlayer')
);

// =============================================
// CHART COMPONENTS - Load for admin/analytics
// =============================================

// Charts are heavy and only needed for specific pages
export const LazyChartComponent = lazy(() => 
  import('./ui/ChartComponent').catch(() => ({
    default: () => (
      <div className="w-full h-64 bg-stone-light/20 rectangular flex items-center justify-center">
        <span className="text-ash">Chart unavailable</span>
      </div>
    )
  }))
);

// =============================================
// SOCIAL COMPONENTS - Load when needed
// =============================================

// Social media embeds - load when user scrolls to them
export const LazySocialEmbed = lazy(() => 
  import('./ui/SocialEmbed')
);

// =============================================
// ACCESSIBILITY COMPONENTS - Load when needed
// =============================================

// Accessibility tools - load when user requests them
export const LazyAccessibilityTools = lazy(() => 
  import('./accessibility/AccessibilityTools')
);

// =============================================
// LOADING STATES FOR EACH COMPONENT
// =============================================

// Generic loading states
export const ComponentLoaders = {
  Dashboard: () => (
    <div className="min-h-screen bg-sanctuary flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-temple-gold/20 rectangular animate-pulse"></div>
        <p className="text-ash">Loading dashboard...</p>
      </div>
    </div>
  ),
  
  Form: () => (
    <div className="bg-white p-6 rectangular border border-stone-light/30">
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i}>
            <div className="h-4 w-24 bg-stone-light/30 rectangular animate-pulse mb-2"></div>
            <div className="h-12 w-full bg-stone-light/20 rectangular animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  ),
  
  Media: () => (
    <div className="w-full h-64 bg-stone-light/20 rectangular animate-pulse flex items-center justify-center">
      <span className="text-ash">Loading media...</span>
    </div>
  ),
  
  Chart: () => (
    <div className="bg-white p-6 rectangular border border-stone-light/30">
      <div className="h-6 w-48 bg-stone-light/50 rectangular animate-pulse mb-6"></div>
      <div className="h-64 w-full bg-stone-light/20 rectangular animate-pulse"></div>
    </div>
  )
};

// =============================================
// PRELOAD UTILITIES
// =============================================

// Preload critical components on user interaction
export const preloadCriticalComponents = () => {
  // Preload booking flow on any CTA hover
  const ctaButtons = document.querySelectorAll('[data-cta="booking"]');
  ctaButtons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      import('./Booking/LazyBookingFlow');
    }, { once: true });
  });
  
  // Preload contact form on contact page visit
  if (window.location.pathname.includes('contact')) {
    import('./forms/ContactForm');
  }
  
  // Preload analytics after user interaction
  const events = ['mousedown', 'touchstart', 'keydown', 'scroll'];
  const preloadAnalytics = () => {
    import('./Analytics/LazyAnalytics');
    events.forEach(event => {
      document.removeEventListener(event, preloadAnalytics);
    });
  };
  
  events.forEach(event => {
    document.addEventListener(event, preloadAnalytics, { once: true, passive: true });
  });
};

// =============================================
// COMPONENT SIZE TRACKING
// =============================================

// Track which components are loaded for performance monitoring
export const trackComponentLoad = (componentName) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'component_loaded', {
      component_name: componentName,
      timestamp: Date.now()
    });
  }
};

// =============================================
// EXPORT ALL LAZY COMPONENTS
// =============================================

export default {
  // Heavy components
  PerformanceDashboard: LazyPerformanceDashboard,
  BookingFlow: LazyBookingFlow,
  PWAInstaller: LazyPWAInstaller,
  Analytics: LazyAnalytics,
  
  // Interactive components
  MapComponent: LazyMapComponent,
  ContactForm: LazyContactForm,
  NewsletterForm: LazyNewsletterForm,
  
  // Media components
  ImageGallery: LazyImageGallery,
  VideoPlayer: LazyVideoPlayer,
  
  // Data components
  ChartComponent: LazyChartComponent,
  SocialEmbed: LazySocialEmbed,
  
  // Accessibility
  AccessibilityTools: LazyAccessibilityTools,
  
  // Utilities
  preloadCriticalComponents,
  trackComponentLoad,
  ComponentLoaders
};
